{"name": "utools-ocr-plugin", "version": "1.0.0", "description": "uTools OCR插件 - 支持多种OCR接口和AI大模型的屏幕截图文字识别工具", "main": "dist/index.html", "scripts": {"setup": "node scripts/setup.js", "start": "node start-dev.js", "dev": "vite", "build": "vue-tsc --noEmit && vite build", "preview": "vite preview", "build:upx": "npm run build && node scripts/build-upx.js"}, "keywords": ["utools", "ocr", "screenshot", "ai", "text-recognition"], "author": "Your Name", "license": "MIT", "devDependencies": {"@types/node": "^20.0.0", "@vitejs/plugin-vue": "^4.0.0", "typescript": "^5.0.0", "vite": "^4.0.0", "vue-tsc": "^1.0.0", "@qc2168/vite-plugin-utools": "^1.0.0", "archiver": "^6.0.0", "@types/archiver": "^6.0.0"}, "dependencies": {"vue": "^3.3.0", "axios": "^1.6.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.1.0", "crypto-js": "^4.2.0"}}