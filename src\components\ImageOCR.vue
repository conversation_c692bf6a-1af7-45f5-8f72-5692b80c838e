<template>
  <div class="image-ocr">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>图片OCR识别</span>
          <el-button-group>
            <el-button :icon="Setting" @click="showSettings = true">设置</el-button>
            <el-button :icon="Clock" @click="showHistory = true">历史</el-button>
          </el-button-group>
        </div>
      </template>

      <div class="content">
        <div class="image-section">
          <div class="image-preview">
            <img :src="imageData" alt="待识别图片" />
          </div>
          
          <div class="actions">
            <el-button 
              type="primary" 
              :icon="DocumentCopy"
              :loading="isRecognizing"
              @click="recognizeText"
            >
              {{ isRecognizing ? '识别中...' : '开始识别' }}
            </el-button>
          </div>
        </div>

        <!-- OCR结果 -->
        <div class="result-section" v-if="ocrResult">
          <div class="result-header">
            <span>识别结果</span>
            <el-button-group>
              <el-button size="small" :icon="CopyDocument" @click="copyResult">复制</el-button>
              <el-button size="small" :icon="Edit" @click="editResult">编辑</el-button>
            </el-button-group>
          </div>
          <el-input
            v-model="ocrResult"
            type="textarea"
            :rows="10"
            :readonly="!isEditing"
            class="result-text"
          />
        </div>
      </div>
    </el-card>

    <!-- 设置对话框 -->
    <Settings v-model="showSettings" />
    
    <!-- 历史记录对话框 -->
    <History v-model="showHistory" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { DocumentCopy, Setting, Clock, CopyDocument, Edit } from '@element-plus/icons-vue'
import Settings from './Settings.vue'
import History from './History.vue'
import { ocrService } from '@/services/ocrService'
import { historyService } from '@/services/historyService'

interface Props {
  imageData: string
}

const props = defineProps<Props>()

const ocrResult = ref<string>('')
const isRecognizing = ref(false)
const isEditing = ref(false)
const showSettings = ref(false)
const showHistory = ref(false)

onMounted(() => {
  // 自动开始识别
  if (props.imageData) {
    recognizeText()
  }
})

// 识别文字
const recognizeText = async () => {
  if (!props.imageData) return
  
  try {
    isRecognizing.value = true
    const result = await ocrService.recognizeImage(props.imageData)
    ocrResult.value = result
    
    // 保存到历史记录
    historyService.addRecord({
      id: Date.now().toString(),
      image: props.imageData,
      text: result,
      timestamp: new Date(),
      type: 'image'
    })
    
    ElMessage.success('识别完成')
  } catch (error) {
    ElMessage.error('识别失败: ' + error)
  } finally {
    isRecognizing.value = false
  }
}

// 复制结果
const copyResult = () => {
  if (window.utools) {
    window.utools.copyText(ocrResult.value)
    ElMessage.success('已复制到剪贴板')
  }
}

// 编辑结果
const editResult = () => {
  isEditing.value = !isEditing.value
}
</script>

<style scoped>
.image-ocr {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content {
  height: calc(100% - 60px);
  display: flex;
  gap: 20px;
}

.image-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.image-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.image-preview img {
  max-width: 100%;
  max-height: 100%;
  object-fit: contain;
}

.actions {
  margin-top: 20px;
  text-align: center;
}

.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-text {
  flex: 1;
}
</style>
