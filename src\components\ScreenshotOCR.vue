<template>
  <div class="screenshot-ocr">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>截图OCR识别</span>
          <el-button-group>
            <el-button :icon="Setting" @click="showSettings = true">设置</el-button>
            <el-button :icon="Clock" @click="showHistory = true">历史</el-button>
          </el-button-group>
        </div>
      </template>

      <div class="content">
        <!-- 截图按钮 -->
        <div class="screenshot-section" v-if="!screenshotImage">
          <el-button 
            type="primary" 
            size="large" 
            :icon="Camera"
            :loading="isCapturing"
            @click="captureScreen"
          >
            {{ isCapturing ? '准备截图中...' : '开始截图' }}
          </el-button>
          <p class="tip">点击按钮后，uTools将隐藏，请在屏幕上选择要识别的区域</p>
        </div>

        <!-- 截图预览和识别 -->
        <div class="ocr-section" v-else>
          <div class="image-preview">
            <img :src="screenshotImage" alt="截图预览" />
            <div class="image-actions">
              <el-button size="small" @click="retakeScreenshot">重新截图</el-button>
              <el-button size="small" type="primary" @click="recognizeText" :loading="isRecognizing">
                {{ isRecognizing ? '识别中...' : '开始识别' }}
              </el-button>
            </div>
          </div>

          <!-- OCR结果 -->
          <div class="result-section" v-if="ocrResult">
            <div class="result-header">
              <span>识别结果</span>
              <el-button-group>
                <el-button size="small" :icon="CopyDocument" @click="copyResult">复制</el-button>
                <el-button size="small" :icon="Edit" @click="editResult">编辑</el-button>
              </el-button-group>
            </div>
            <el-input
              v-model="ocrResult"
              type="textarea"
              :rows="8"
              :readonly="!isEditing"
              class="result-text"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 设置对话框 -->
    <Settings v-model="showSettings" />
    
    <!-- 历史记录对话框 -->
    <History v-model="showHistory" />
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Camera, Setting, Clock, CopyDocument, Edit } from '@element-plus/icons-vue'
import Settings from './Settings.vue'
import History from './History.vue'
import { ocrService } from '@/services/ocrService'
import { historyService } from '@/services/historyService'

const screenshotImage = ref<string>('')
const ocrResult = ref<string>('')
const isCapturing = ref(false)
const isRecognizing = ref(false)
const isEditing = ref(false)
const showSettings = ref(false)
const showHistory = ref(false)

// 截图功能
const captureScreen = async () => {
  try {
    isCapturing.value = true
    
    // 隐藏uTools窗口
    if (window.utools) {
      window.utools.hideMainWindow()
    }
    
    // 调用preload中的截图功能
    const imageData = await window.preload?.captureScreen()
    
    if (imageData) {
      screenshotImage.value = imageData
      // 显示uTools窗口
      if (window.utools) {
        window.utools.showMainWindow()
      }
    }
  } catch (error) {
    ElMessage.error('截图失败: ' + error)
  } finally {
    isCapturing.value = false
  }
}

// 重新截图
const retakeScreenshot = () => {
  screenshotImage.value = ''
  ocrResult.value = ''
  isEditing.value = false
}

// 识别文字
const recognizeText = async () => {
  if (!screenshotImage.value) return
  
  try {
    isRecognizing.value = true
    const result = await ocrService.recognizeImage(screenshotImage.value)
    ocrResult.value = result
    
    // 保存到历史记录
    historyService.addRecord({
      id: Date.now().toString(),
      image: screenshotImage.value,
      text: result,
      timestamp: new Date(),
      type: 'screenshot'
    })
    
    ElMessage.success('识别完成')
  } catch (error) {
    ElMessage.error('识别失败: ' + error)
  } finally {
    isRecognizing.value = false
  }
}

// 复制结果
const copyResult = () => {
  if (window.utools) {
    window.utools.copyText(ocrResult.value)
    ElMessage.success('已复制到剪贴板')
  }
}

// 编辑结果
const editResult = () => {
  isEditing.value = !isEditing.value
}
</script>

<style scoped>
.screenshot-ocr {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content {
  height: calc(100% - 60px);
}

.screenshot-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
}

.tip {
  margin-top: 20px;
  color: #666;
  text-align: center;
}

.ocr-section {
  display: flex;
  gap: 20px;
  height: 100%;
}

.image-preview {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.image-preview img {
  max-width: 100%;
  max-height: 300px;
  object-fit: contain;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.image-actions {
  margin-top: 10px;
  display: flex;
  gap: 10px;
}

.result-section {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-text {
  flex: 1;
}
</style>
