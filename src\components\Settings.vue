<template>
  <el-dialog 
    v-model="visible" 
    title="OCR设置" 
    width="70%"
    :before-close="handleClose"
  >
    <el-tabs v-model="activeTab" type="card">
      <!-- OCR服务设置 -->
      <el-tab-pane label="OCR服务" name="ocr">
        <el-form :model="ocrSettings" label-width="120px">
          <el-form-item label="默认OCR服务">
            <el-select v-model="ocrSettings.defaultService" placeholder="请选择OCR服务">
              <el-option label="百度OCR" value="baidu" />
              <el-option label="腾讯云OCR" value="tencent" />
              <el-option label="阿里云OCR" value="aliyun" />
              <el-option label="AI大模型OCR" value="ai" />
            </el-select>
          </el-form-item>

          <!-- 百度OCR设置 -->
          <el-collapse v-model="activeCollapse">
            <el-collapse-item title="百度OCR配置" name="baidu">
              <el-form-item label="API Key">
                <el-input 
                  v-model="ocrSettings.baidu.apiKey" 
                  placeholder="请输入百度OCR API Key"
                  show-password
                />
              </el-form-item>
              <el-form-item label="Secret Key">
                <el-input 
                  v-model="ocrSettings.baidu.secretKey" 
                  placeholder="请输入百度OCR Secret Key"
                  show-password
                />
              </el-form-item>
              <el-form-item label="识别类型">
                <el-select v-model="ocrSettings.baidu.type">
                  <el-option label="通用文字识别" value="general_basic" />
                  <el-option label="通用文字识别(高精度版)" value="accurate_basic" />
                  <el-option label="通用文字识别(含位置信息版)" value="general" />
                </el-select>
              </el-form-item>
            </el-collapse-item>

            <!-- 腾讯云OCR设置 -->
            <el-collapse-item title="腾讯云OCR配置" name="tencent">
              <el-form-item label="Secret ID">
                <el-input 
                  v-model="ocrSettings.tencent.secretId" 
                  placeholder="请输入腾讯云Secret ID"
                  show-password
                />
              </el-form-item>
              <el-form-item label="Secret Key">
                <el-input 
                  v-model="ocrSettings.tencent.secretKey" 
                  placeholder="请输入腾讯云Secret Key"
                  show-password
                />
              </el-form-item>
              <el-form-item label="地域">
                <el-select v-model="ocrSettings.tencent.region">
                  <el-option label="北京" value="ap-beijing" />
                  <el-option label="上海" value="ap-shanghai" />
                  <el-option label="广州" value="ap-guangzhou" />
                </el-select>
              </el-form-item>
            </el-collapse-item>

            <!-- 阿里云OCR设置 -->
            <el-collapse-item title="阿里云OCR配置" name="aliyun">
              <el-form-item label="Access Key ID">
                <el-input 
                  v-model="ocrSettings.aliyun.accessKeyId" 
                  placeholder="请输入阿里云Access Key ID"
                  show-password
                />
              </el-form-item>
              <el-form-item label="Access Key Secret">
                <el-input 
                  v-model="ocrSettings.aliyun.accessKeySecret" 
                  placeholder="请输入阿里云Access Key Secret"
                  show-password
                />
              </el-form-item>
            </el-collapse-item>

            <!-- AI大模型OCR设置 -->
            <el-collapse-item title="AI大模型OCR配置" name="ai">
              <el-form-item label="AI服务商">
                <el-select v-model="ocrSettings.ai.provider">
                  <el-option label="OpenAI GPT-4V" value="openai" />
                  <el-option label="Claude 3" value="claude" />
                  <el-option label="Google Gemini" value="gemini" />
                  <el-option label="通义千问VL" value="qwen" />
                </el-select>
              </el-form-item>
              <el-form-item label="API Key">
                <el-input 
                  v-model="ocrSettings.ai.apiKey" 
                  placeholder="请输入AI服务API Key"
                  show-password
                />
              </el-form-item>
              <el-form-item label="API Base URL" v-if="ocrSettings.ai.provider === 'openai'">
                <el-input 
                  v-model="ocrSettings.ai.baseUrl" 
                  placeholder="可选，自定义API地址"
                />
              </el-form-item>
              <el-form-item label="模型">
                <el-input 
                  v-model="ocrSettings.ai.model" 
                  :placeholder="getModelPlaceholder()"
                />
              </el-form-item>
              <el-form-item label="流式输出">
                <el-switch v-model="ocrSettings.ai.stream" />
              </el-form-item>
            </el-collapse-item>
          </el-collapse>
        </el-form>
      </el-tab-pane>

      <!-- 通用设置 -->
      <el-tab-pane label="通用设置" name="general">
        <el-form :model="generalSettings" label-width="120px">
          <el-form-item label="自动复制结果">
            <el-switch v-model="generalSettings.autoCopy" />
          </el-form-item>
          <el-form-item label="保存历史记录">
            <el-switch v-model="generalSettings.saveHistory" />
          </el-form-item>
          <el-form-item label="历史记录数量">
            <el-input-number 
              v-model="generalSettings.maxHistoryCount" 
              :min="10" 
              :max="1000" 
              :step="10"
            />
          </el-form-item>
          <el-form-item label="截图快捷键">
            <el-input 
              v-model="generalSettings.screenshotHotkey" 
              placeholder="如: Ctrl+Alt+S"
              readonly
              @click="recordHotkey"
            />
          </el-form-item>
        </el-form>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="saveSettings">保存设置</el-button>
        <el-button @click="testConnection">测试连接</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, watch, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { settingsService } from '@/services/settingsService'
import { ocrService } from '@/services/ocrService'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = ref(false)
const activeTab = ref('ocr')
const activeCollapse = ref(['baidu'])

const ocrSettings = ref({
  defaultService: 'baidu',
  baidu: {
    apiKey: '',
    secretKey: '',
    type: 'general_basic'
  },
  tencent: {
    secretId: '',
    secretKey: '',
    region: 'ap-beijing'
  },
  aliyun: {
    accessKeyId: '',
    accessKeySecret: ''
  },
  ai: {
    provider: 'openai',
    apiKey: '',
    baseUrl: '',
    model: 'gpt-4-vision-preview',
    stream: false
  }
})

const generalSettings = ref({
  autoCopy: true,
  saveHistory: true,
  maxHistoryCount: 100,
  screenshotHotkey: 'Ctrl+Alt+S'
})

watch(() => props.modelValue, (val) => {
  visible.value = val
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

onMounted(() => {
  loadSettings()
})

// 加载设置
const loadSettings = async () => {
  const settings = await settingsService.getSettings()
  if (settings.ocr) {
    ocrSettings.value = { ...ocrSettings.value, ...settings.ocr }
  }
  if (settings.general) {
    generalSettings.value = { ...generalSettings.value, ...settings.general }
  }
}

// 保存设置
const saveSettings = async () => {
  try {
    await settingsService.saveSettings({
      ocr: ocrSettings.value,
      general: generalSettings.value
    })
    ElMessage.success('设置保存成功')
    handleClose()
  } catch (error) {
    ElMessage.error('设置保存失败: ' + error)
  }
}

// 测试连接
const testConnection = async () => {
  try {
    // 创建一个测试图片（1x1像素的透明PNG）
    const testImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    
    await ocrService.recognizeImage(testImage, ocrSettings.value.defaultService)
    ElMessage.success('连接测试成功')
  } catch (error) {
    ElMessage.error('连接测试失败: ' + error)
  }
}

// 获取模型占位符
const getModelPlaceholder = () => {
  const placeholders = {
    openai: 'gpt-4-vision-preview',
    claude: 'claude-3-opus-20240229',
    gemini: 'gemini-pro-vision',
    qwen: 'qwen-vl-plus'
  }
  return placeholders[ocrSettings.value.ai.provider as keyof typeof placeholders] || ''
}

// 记录快捷键
const recordHotkey = () => {
  ElMessage.info('快捷键录制功能待实现')
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}
</style>
