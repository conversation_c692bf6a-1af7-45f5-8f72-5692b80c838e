import axios from 'axios'
import { settingsService } from './settingsService'

export interface OCRResult {
  text: string
  confidence?: number
  words?: Array<{
    text: string
    confidence: number
    location?: {
      left: number
      top: number
      width: number
      height: number
    }
  }>
}

class OCRService {
  // 主要识别方法
  async recognizeImage(imageData: string, serviceType?: string): Promise<string> {
    const settings = await settingsService.getSettings()
    const service = serviceType || settings.ocr?.defaultService || 'baidu'

    try {
      switch (service) {
        case 'baidu':
          return await this.recognizeWithBaidu(imageData, settings.ocr?.baidu)
        case 'tencent':
          return await this.recognizeWithTencent(imageData, settings.ocr?.tencent)
        case 'aliyun':
          return await this.recognizeWithAliyun(imageData, settings.ocr?.aliyun)
        case 'ai':
          return await this.recognizeWithAI(imageData, settings.ocr?.ai)
        default:
          throw new Error('不支持的OCR服务类型')
      }
    } catch (error) {
      console.error('OCR识别失败:', error)
      throw error
    }
  }

  // 百度OCR
  private async recognizeWithBaidu(imageData: string, config: any): Promise<string> {
    if (!config?.apiKey || !config?.secretKey) {
      throw new Error('百度OCR配置不完整')
    }

    // 获取access_token
    const tokenResponse = await axios.post(
      'https://aip.baidubce.com/oauth/2.0/token',
      null,
      {
        params: {
          grant_type: 'client_credentials',
          client_id: config.apiKey,
          client_secret: config.secretKey
        }
      }
    )

    const accessToken = tokenResponse.data.access_token
    if (!accessToken) {
      throw new Error('获取百度OCR访问令牌失败')
    }

    // 处理图片数据
    const base64Image = this.extractBase64(imageData)
    
    // 调用OCR接口
    const ocrResponse = await axios.post(
      `https://aip.baidubce.com/rest/2.0/ocr/v1/${config.type || 'general_basic'}`,
      `image=${encodeURIComponent(base64Image)}`,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        },
        params: {
          access_token: accessToken
        }
      }
    )

    if (ocrResponse.data.error_code) {
      throw new Error(`百度OCR错误: ${ocrResponse.data.error_msg}`)
    }

    // 提取文字
    const words = ocrResponse.data.words_result || []
    return words.map((item: any) => item.words).join('\n')
  }

  // 腾讯云OCR
  private async recognizeWithTencent(imageData: string, config: any): Promise<string> {
    if (!config?.secretId || !config?.secretKey) {
      throw new Error('腾讯云OCR配置不完整')
    }

    // 这里需要实现腾讯云的签名算法
    // 由于签名算法较复杂，建议使用腾讯云SDK
    throw new Error('腾讯云OCR暂未实现，请使用其他服务')
  }

  // 阿里云OCR
  private async recognizeWithAliyun(imageData: string, config: any): Promise<string> {
    if (!config?.accessKeyId || !config?.accessKeySecret) {
      throw new Error('阿里云OCR配置不完整')
    }

    // 这里需要实现阿里云的签名算法
    // 由于签名算法较复杂，建议使用阿里云SDK
    throw new Error('阿里云OCR暂未实现，请使用其他服务')
  }

  // AI大模型OCR
  private async recognizeWithAI(imageData: string, config: any): Promise<string> {
    if (!config?.apiKey) {
      throw new Error('AI服务配置不完整')
    }

    switch (config.provider) {
      case 'openai':
        return await this.recognizeWithOpenAI(imageData, config)
      case 'claude':
        return await this.recognizeWithClaude(imageData, config)
      case 'gemini':
        return await this.recognizeWithGemini(imageData, config)
      case 'qwen':
        return await this.recognizeWithQwen(imageData, config)
      default:
        throw new Error('不支持的AI服务提供商')
    }
  }

  // OpenAI GPT-4V
  private async recognizeWithOpenAI(imageData: string, config: any): Promise<string> {
    const baseUrl = config.baseUrl || 'https://api.openai.com'
    const model = config.model || 'gpt-4-vision-preview'

    const response = await axios.post(
      `${baseUrl}/v1/chat/completions`,
      {
        model,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释或格式。'
              },
              {
                type: 'image_url',
                image_url: {
                  url: imageData
                }
              }
            ]
          }
        ],
        max_tokens: 4000,
        stream: config.stream || false
      },
      {
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data.error) {
      throw new Error(`OpenAI错误: ${response.data.error.message}`)
    }

    return response.data.choices[0]?.message?.content || ''
  }

  // Claude 3
  private async recognizeWithClaude(imageData: string, config: any): Promise<string> {
    const response = await axios.post(
      'https://api.anthropic.com/v1/messages',
      {
        model: config.model || 'claude-3-opus-20240229',
        max_tokens: 4000,
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释或格式。'
              },
              {
                type: 'image',
                source: {
                  type: 'base64',
                  media_type: 'image/jpeg',
                  data: this.extractBase64(imageData)
                }
              }
            ]
          }
        ]
      },
      {
        headers: {
          'x-api-key': config.apiKey,
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01'
        }
      }
    )

    if (response.data.error) {
      throw new Error(`Claude错误: ${response.data.error.message}`)
    }

    return response.data.content[0]?.text || ''
  }

  // Google Gemini
  private async recognizeWithGemini(imageData: string, config: any): Promise<string> {
    const model = config.model || 'gemini-pro-vision'
    
    const response = await axios.post(
      `https://generativelanguage.googleapis.com/v1beta/models/${model}:generateContent?key=${config.apiKey}`,
      {
        contents: [
          {
            parts: [
              {
                text: '请识别图片中的所有文字内容，直接输出文字，不要添加任何解释或格式。'
              },
              {
                inline_data: {
                  mime_type: 'image/jpeg',
                  data: this.extractBase64(imageData)
                }
              }
            ]
          }
        ]
      },
      {
        headers: {
          'Content-Type': 'application/json'
        }
      }
    )

    if (response.data.error) {
      throw new Error(`Gemini错误: ${response.data.error.message}`)
    }

    return response.data.candidates[0]?.content?.parts[0]?.text || ''
  }

  // 通义千问VL
  private async recognizeWithQwen(imageData: string, config: any): Promise<string> {
    // 通义千问VL的实现
    throw new Error('通义千问VL暂未实现')
  }

  // 提取base64数据
  private extractBase64(imageData: string): string {
    if (imageData.startsWith('data:')) {
      return imageData.split(',')[1]
    }
    return imageData
  }
}

export const ocrService = new OCRService()
