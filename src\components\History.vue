<template>
  <el-dialog 
    v-model="visible" 
    title="OCR历史记录" 
    width="80%"
    :before-close="handleClose"
  >
    <div class="history-content">
      <!-- 搜索和筛选 -->
      <div class="search-bar">
        <el-input
          v-model="searchText"
          placeholder="搜索历史记录..."
          :prefix-icon="Search"
          clearable
          style="width: 300px;"
        />
        <el-select v-model="filterType" placeholder="类型筛选" style="width: 120px; margin-left: 10px;">
          <el-option label="全部" value="" />
          <el-option label="截图" value="screenshot" />
          <el-option label="图片" value="image" />
          <el-option label="文件" value="file" />
        </el-select>
        <el-button :icon="Delete" @click="clearHistory" style="margin-left: 10px;">清空历史</el-button>
      </div>

      <!-- 历史记录列表 -->
      <div class="history-list">
        <el-empty v-if="filteredHistory.length === 0" description="暂无历史记录" />
        
        <div 
          v-else
          class="history-item" 
          v-for="record in paginatedHistory" 
          :key="record.id"
        >
          <div class="item-header">
            <div class="item-info">
              <el-tag :type="getTypeColor(record.type)" size="small">
                {{ getTypeLabel(record.type) }}
              </el-tag>
              <span class="timestamp">{{ formatTime(record.timestamp) }}</span>
              <span v-if="record.fileName" class="file-name">{{ record.fileName }}</span>
            </div>
            <div class="item-actions">
              <el-button size="small" :icon="CopyDocument" @click="copyText(record.text)">复制</el-button>
              <el-button size="small" :icon="View" @click="viewRecord(record)">查看</el-button>
              <el-button size="small" :icon="Delete" type="danger" @click="deleteRecord(record.id)">删除</el-button>
            </div>
          </div>
          
          <div class="item-content">
            <div class="image-preview" v-if="record.image">
              <img :src="record.image" alt="预览图" @click="previewImage(record.image)" />
            </div>
            <div class="text-content">
              <p class="text-preview">{{ record.text.substring(0, 200) }}{{ record.text.length > 200 ? '...' : '' }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div class="pagination" v-if="filteredHistory.length > pageSize">
        <el-pagination
          v-model:current-page="currentPage"
          :page-size="pageSize"
          :total="filteredHistory.length"
          layout="prev, pager, next, total"
          small
        />
      </div>
    </div>

    <!-- 详情查看对话框 -->
    <el-dialog v-model="showDetail" title="详细内容" width="60%" append-to-body>
      <div class="detail-content" v-if="selectedRecord">
        <div class="detail-image" v-if="selectedRecord.image">
          <img :src="selectedRecord.image" alt="原图" style="max-width: 100%; max-height: 300px;" />
        </div>
        <div class="detail-text">
          <h4>识别文本：</h4>
          <el-input
            v-model="selectedRecord.text"
            type="textarea"
            :rows="10"
            readonly
          />
        </div>
      </div>
      <template #footer>
        <el-button @click="showDetail = false">关闭</el-button>
        <el-button type="primary" @click="copyText(selectedRecord?.text || '')">复制文本</el-button>
      </template>
    </el-dialog>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="showImagePreview" title="图片预览" width="60%" append-to-body>
      <div class="image-preview-content">
        <img :src="previewImageSrc" alt="图片预览" style="max-width: 100%;" />
      </div>
    </el-dialog>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Delete, CopyDocument, View } from '@element-plus/icons-vue'
import { historyService, type HistoryRecord } from '@/services/historyService'

interface Props {
  modelValue: boolean
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const visible = ref(false)
const searchText = ref('')
const filterType = ref('')
const currentPage = ref(1)
const pageSize = 10
const historyRecords = ref<HistoryRecord[]>([])
const selectedRecord = ref<HistoryRecord | null>(null)
const showDetail = ref(false)
const showImagePreview = ref(false)
const previewImageSrc = ref('')

watch(() => props.modelValue, (val) => {
  visible.value = val
  if (val) {
    loadHistory()
  }
})

watch(visible, (val) => {
  emit('update:modelValue', val)
})

onMounted(() => {
  loadHistory()
})

// 过滤后的历史记录
const filteredHistory = computed(() => {
  let filtered = historyRecords.value

  // 类型筛选
  if (filterType.value) {
    filtered = filtered.filter(record => record.type === filterType.value)
  }

  // 文本搜索
  if (searchText.value) {
    const search = searchText.value.toLowerCase()
    filtered = filtered.filter(record => 
      record.text.toLowerCase().includes(search) ||
      (record.fileName && record.fileName.toLowerCase().includes(search))
    )
  }

  return filtered.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
})

// 分页后的历史记录
const paginatedHistory = computed(() => {
  const start = (currentPage.value - 1) * pageSize
  const end = start + pageSize
  return filteredHistory.value.slice(start, end)
})

// 加载历史记录
const loadHistory = async () => {
  historyRecords.value = await historyService.getHistory()
}

// 获取类型颜色
const getTypeColor = (type: string) => {
  const colors = {
    screenshot: 'primary',
    image: 'success',
    file: 'warning'
  }
  return colors[type as keyof typeof colors] || 'info'
}

// 获取类型标签
const getTypeLabel = (type: string) => {
  const labels = {
    screenshot: '截图',
    image: '图片',
    file: '文件'
  }
  return labels[type as keyof typeof labels] || type
}

// 格式化时间
const formatTime = (timestamp: Date) => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 1天内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
  }
}

// 复制文本
const copyText = (text: string) => {
  if (window.utools) {
    window.utools.copyText(text)
    ElMessage.success('已复制到剪贴板')
  }
}

// 查看记录详情
const viewRecord = (record: HistoryRecord) => {
  selectedRecord.value = record
  showDetail.value = true
}

// 预览图片
const previewImage = (imageSrc: string) => {
  previewImageSrc.value = imageSrc
  showImagePreview.value = true
}

// 删除记录
const deleteRecord = async (id: string) => {
  try {
    await ElMessageBox.confirm('确定要删除这条记录吗？', '确认删除', {
      type: 'warning'
    })
    
    await historyService.deleteRecord(id)
    await loadHistory()
    ElMessage.success('删除成功')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败: ' + error)
    }
  }
}

// 清空历史
const clearHistory = async () => {
  try {
    await ElMessageBox.confirm('确定要清空所有历史记录吗？此操作不可恢复！', '确认清空', {
      type: 'warning'
    })
    
    await historyService.clearHistory()
    await loadHistory()
    ElMessage.success('历史记录已清空')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('清空失败: ' + error)
    }
  }
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
}
</script>

<style scoped>
.history-content {
  height: 500px;
  display: flex;
  flex-direction: column;
}

.search-bar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.history-list {
  flex: 1;
  overflow-y: auto;
}

.history-item {
  border: 1px solid #eee;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  background: #fff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.item-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.timestamp {
  color: #666;
  font-size: 12px;
}

.file-name {
  color: #409eff;
  font-size: 12px;
}

.item-actions {
  display: flex;
  gap: 5px;
}

.item-content {
  display: flex;
  gap: 15px;
}

.image-preview {
  flex-shrink: 0;
}

.image-preview img {
  width: 80px;
  height: 80px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
}

.text-content {
  flex: 1;
}

.text-preview {
  margin: 0;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
}

.pagination {
  margin-top: 20px;
  text-align: center;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
}

.detail-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.detail-image {
  text-align: center;
}

.image-preview-content {
  text-align: center;
}
</style>
