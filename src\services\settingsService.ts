export interface OCRSettings {
  defaultService: string
  baidu: {
    apiKey: string
    secretKey: string
    type: string
  }
  tencent: {
    secretId: string
    secretKey: string
    region: string
  }
  aliyun: {
    accessKeyId: string
    accessKeySecret: string
  }
  ai: {
    provider: string
    apiKey: string
    baseUrl: string
    model: string
    stream: boolean
  }
}

export interface GeneralSettings {
  autoCopy: boolean
  saveHistory: boolean
  maxHistoryCount: number
  screenshotHotkey: string
}

export interface Settings {
  ocr?: OCRSettings
  general?: GeneralSettings
}

class SettingsService {
  private readonly SETTINGS_KEY = 'ocr_plugin_settings'

  // 获取设置
  async getSettings(): Promise<Settings> {
    try {
      if (window.utools?.db) {
        const doc = window.utools.db.get(this.SETTINGS_KEY)
        return doc?.data || this.getDefaultSettings()
      }
      
      // 如果utools不可用，使用localStorage
      const stored = localStorage.getItem(this.SETTINGS_KEY)
      return stored ? JSON.parse(stored) : this.getDefaultSettings()
    } catch (error) {
      console.error('获取设置失败:', error)
      return this.getDefaultSettings()
    }
  }

  // 保存设置
  async saveSettings(settings: Settings): Promise<void> {
    try {
      if (window.utools?.db) {
        // 使用utools数据库
        const existingDoc = window.utools.db.get(this.SETTINGS_KEY)
        if (existingDoc) {
          window.utools.db.put({
            _id: this.SETTINGS_KEY,
            _rev: existingDoc._rev,
            data: settings
          })
        } else {
          window.utools.db.put({
            _id: this.SETTINGS_KEY,
            data: settings
          })
        }
      } else {
        // 使用localStorage作为备选
        localStorage.setItem(this.SETTINGS_KEY, JSON.stringify(settings))
      }
    } catch (error) {
      console.error('保存设置失败:', error)
      throw error
    }
  }

  // 获取默认设置
  private getDefaultSettings(): Settings {
    return {
      ocr: {
        defaultService: 'baidu',
        baidu: {
          apiKey: '',
          secretKey: '',
          type: 'general_basic'
        },
        tencent: {
          secretId: '',
          secretKey: '',
          region: 'ap-beijing'
        },
        aliyun: {
          accessKeyId: '',
          accessKeySecret: ''
        },
        ai: {
          provider: 'openai',
          apiKey: '',
          baseUrl: 'https://api.openai.com',
          model: 'gpt-4-vision-preview',
          stream: false
        }
      },
      general: {
        autoCopy: true,
        saveHistory: true,
        maxHistoryCount: 100,
        screenshotHotkey: 'Ctrl+Alt+S'
      }
    }
  }

  // 重置设置
  async resetSettings(): Promise<void> {
    await this.saveSettings(this.getDefaultSettings())
  }

  // 导出设置
  async exportSettings(): Promise<string> {
    const settings = await this.getSettings()
    return JSON.stringify(settings, null, 2)
  }

  // 导入设置
  async importSettings(settingsJson: string): Promise<void> {
    try {
      const settings = JSON.parse(settingsJson)
      await this.saveSettings(settings)
    } catch (error) {
      throw new Error('设置格式无效')
    }
  }

  // 验证设置
  validateSettings(settings: Settings): boolean {
    // 基本验证逻辑
    if (!settings.ocr || !settings.general) {
      return false
    }

    // 验证OCR设置
    const ocr = settings.ocr
    if (!ocr.defaultService) {
      return false
    }

    // 根据默认服务验证相应配置
    switch (ocr.defaultService) {
      case 'baidu':
        if (!ocr.baidu.apiKey || !ocr.baidu.secretKey) {
          return false
        }
        break
      case 'tencent':
        if (!ocr.tencent.secretId || !ocr.tencent.secretKey) {
          return false
        }
        break
      case 'aliyun':
        if (!ocr.aliyun.accessKeyId || !ocr.aliyun.accessKeySecret) {
          return false
        }
        break
      case 'ai':
        if (!ocr.ai.apiKey) {
          return false
        }
        break
    }

    return true
  }

  // 获取特定服务的配置
  async getServiceConfig(service: string): Promise<any> {
    const settings = await this.getSettings()
    return settings.ocr?.[service as keyof OCRSettings]
  }

  // 更新特定服务的配置
  async updateServiceConfig(service: string, config: any): Promise<void> {
    const settings = await this.getSettings()
    if (settings.ocr) {
      (settings.ocr as any)[service] = config
      await this.saveSettings(settings)
    }
  }
}

export const settingsService = new SettingsService()
