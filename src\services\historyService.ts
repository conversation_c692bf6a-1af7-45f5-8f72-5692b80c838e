export interface HistoryRecord {
  id: string
  image: string
  text: string
  timestamp: Date
  type: 'screenshot' | 'image' | 'file'
  fileName?: string
  confidence?: number
}

class HistoryService {
  private readonly HISTORY_KEY = 'ocr_plugin_history'
  private readonly MAX_RECORDS = 1000 // 最大记录数

  // 获取历史记录
  async getHistory(): Promise<HistoryRecord[]> {
    try {
      if (window.utools?.db) {
        const doc = window.utools.db.get(this.HISTORY_KEY)
        const records = doc?.data || []
        return records.map((record: any) => ({
          ...record,
          timestamp: new Date(record.timestamp)
        }))
      }
      
      // 使用localStorage作为备选
      const stored = localStorage.getItem(this.HISTORY_KEY)
      const records = stored ? JSON.parse(stored) : []
      return records.map((record: any) => ({
        ...record,
        timestamp: new Date(record.timestamp)
      }))
    } catch (error) {
      console.error('获取历史记录失败:', error)
      return []
    }
  }

  // 添加记录
  async addRecord(record: HistoryRecord): Promise<void> {
    try {
      const history = await this.getHistory()
      
      // 添加新记录到开头
      history.unshift(record)
      
      // 限制记录数量
      if (history.length > this.MAX_RECORDS) {
        history.splice(this.MAX_RECORDS)
      }
      
      await this.saveHistory(history)
    } catch (error) {
      console.error('添加历史记录失败:', error)
      throw error
    }
  }

  // 删除记录
  async deleteRecord(id: string): Promise<void> {
    try {
      const history = await this.getHistory()
      const filteredHistory = history.filter(record => record.id !== id)
      await this.saveHistory(filteredHistory)
    } catch (error) {
      console.error('删除历史记录失败:', error)
      throw error
    }
  }

  // 清空历史记录
  async clearHistory(): Promise<void> {
    try {
      await this.saveHistory([])
    } catch (error) {
      console.error('清空历史记录失败:', error)
      throw error
    }
  }

  // 搜索历史记录
  async searchHistory(query: string): Promise<HistoryRecord[]> {
    const history = await this.getHistory()
    const lowerQuery = query.toLowerCase()
    
    return history.filter(record => 
      record.text.toLowerCase().includes(lowerQuery) ||
      (record.fileName && record.fileName.toLowerCase().includes(lowerQuery))
    )
  }

  // 按类型筛选历史记录
  async getHistoryByType(type: HistoryRecord['type']): Promise<HistoryRecord[]> {
    const history = await this.getHistory()
    return history.filter(record => record.type === type)
  }

  // 获取最近的记录
  async getRecentHistory(count: number = 10): Promise<HistoryRecord[]> {
    const history = await this.getHistory()
    return history.slice(0, count)
  }

  // 导出历史记录
  async exportHistory(): Promise<string> {
    const history = await this.getHistory()
    return JSON.stringify(history, null, 2)
  }

  // 导入历史记录
  async importHistory(historyJson: string): Promise<void> {
    try {
      const importedHistory = JSON.parse(historyJson)
      if (!Array.isArray(importedHistory)) {
        throw new Error('历史记录格式无效')
      }
      
      const validRecords = importedHistory.filter(this.validateRecord)
      await this.saveHistory(validRecords)
    } catch (error) {
      throw new Error('导入历史记录失败: ' + error)
    }
  }

  // 获取统计信息
  async getStatistics(): Promise<{
    totalRecords: number
    screenshotCount: number
    imageCount: number
    fileCount: number
    todayCount: number
    weekCount: number
  }> {
    const history = await this.getHistory()
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)

    return {
      totalRecords: history.length,
      screenshotCount: history.filter(r => r.type === 'screenshot').length,
      imageCount: history.filter(r => r.type === 'image').length,
      fileCount: history.filter(r => r.type === 'file').length,
      todayCount: history.filter(r => r.timestamp >= today).length,
      weekCount: history.filter(r => r.timestamp >= weekAgo).length
    }
  }

  // 保存历史记录
  private async saveHistory(history: HistoryRecord[]): Promise<void> {
    try {
      if (window.utools?.db) {
        const existingDoc = window.utools.db.get(this.HISTORY_KEY)
        if (existingDoc) {
          window.utools.db.put({
            _id: this.HISTORY_KEY,
            _rev: existingDoc._rev,
            data: history
          })
        } else {
          window.utools.db.put({
            _id: this.HISTORY_KEY,
            data: history
          })
        }
      } else {
        localStorage.setItem(this.HISTORY_KEY, JSON.stringify(history))
      }
    } catch (error) {
      console.error('保存历史记录失败:', error)
      throw error
    }
  }

  // 验证记录格式
  private validateRecord(record: any): boolean {
    return (
      record &&
      typeof record.id === 'string' &&
      typeof record.text === 'string' &&
      typeof record.image === 'string' &&
      record.timestamp &&
      ['screenshot', 'image', 'file'].includes(record.type)
    )
  }

  // 清理过期记录
  async cleanupOldRecords(daysToKeep: number = 30): Promise<void> {
    const history = await this.getHistory()
    const cutoffDate = new Date()
    cutoffDate.setDate(cutoffDate.getDate() - daysToKeep)
    
    const filteredHistory = history.filter(record => record.timestamp >= cutoffDate)
    await this.saveHistory(filteredHistory)
  }
}

export const historyService = new HistoryService()
