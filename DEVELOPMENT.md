# uTools OCR插件开发指南

## 📋 开发环境搭建

### 前置要求
- Node.js 16+ 
- npm 或 pnpm
- uTools 客户端
- 代码编辑器（推荐 VS Code）

### 安装依赖
```bash
# 使用npm
npm install

# 或使用pnpm（推荐）
pnpm install
```

### 开发模式
```bash
# 启动开发服务器
npm run dev

# 服务器将在 http://localhost:5173 启动
```

### 在uTools中调试
1. 打开uTools开发者工具
2. 新建插件项目
3. 选择项目根目录下的 `plugin.json` 文件
4. 启动插件进行调试

## 🏗️ 项目架构

### 目录结构
```
├── src/
│   ├── components/          # Vue组件
│   │   ├── ScreenshotOCR.vue   # 截图OCR组件
│   │   ├── ImageOCR.vue        # 图片OCR组件
│   │   ├── FileOCR.vue         # 文件OCR组件
│   │   ├── Settings.vue        # 设置组件
│   │   └── History.vue         # 历史记录组件
│   ├── services/           # 业务服务层
│   │   ├── ocrService.ts       # OCR服务
│   │   ├── settingsService.ts  # 设置服务
│   │   └── historyService.ts   # 历史记录服务
│   ├── types/              # TypeScript类型定义
│   │   └── global.d.ts         # 全局类型
│   ├── App.vue             # 主应用组件
│   └── main.ts             # 应用入口
├── scripts/                # 构建脚本
│   └── build-upx.js        # UPX包构建脚本
├── preload.js              # uTools预加载脚本
├── plugin.json             # uTools插件配置
├── index.html              # HTML入口
├── vite.config.ts          # Vite配置
└── package.json            # 项目配置
```

### 核心模块说明

#### 1. OCR服务 (ocrService.ts)
负责调用各种OCR API接口：
- 百度OCR API
- 腾讯云OCR API  
- 阿里云OCR API
- AI大模型OCR (OpenAI, Claude, Gemini等)

#### 2. 设置服务 (settingsService.ts)
管理插件配置：
- API密钥存储
- 服务选择配置
- 通用设置管理

#### 3. 历史记录服务 (historyService.ts)
管理识别历史：
- 记录存储和检索
- 搜索和筛选
- 数据导入导出

#### 4. 预加载脚本 (preload.js)
提供系统级功能：
- 屏幕截图
- 文件操作
- 剪贴板操作
- 系统信息获取

## 🔧 开发规范

### 代码风格
- 使用TypeScript进行类型检查
- 遵循Vue 3 Composition API规范
- 使用ESLint和Prettier格式化代码
- 组件命名使用PascalCase
- 文件命名使用camelCase

### 组件开发
```vue
<template>
  <!-- 模板内容 -->
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
// 组件逻辑
</script>

<style scoped>
/* 样式 */
</style>
```

### 服务开发
```typescript
class ExampleService {
  async method(): Promise<ReturnType> {
    // 服务逻辑
  }
}

export const exampleService = new ExampleService()
```

## 🧪 测试指南

### 功能测试
1. **截图OCR测试**
   - 测试不同分辨率的截图
   - 测试不同字体和大小的文字
   - 测试倾斜和模糊的图片

2. **API接口测试**
   - 测试各种OCR服务的连接
   - 测试错误处理和重试机制
   - 测试API限流和配额

3. **历史记录测试**
   - 测试记录的保存和读取
   - 测试搜索和筛选功能
   - 测试数据导入导出

### 性能测试
- 大图片处理性能
- 批量文件处理性能
- 内存使用情况
- 启动速度测试

## 📦 构建和发布

### 构建生产版本
```bash
# 构建项目
npm run build

# 构建UPX包
npm run build:upx
```

### 发布流程
1. 更新版本号 (`package.json`)
2. 更新插件配置 (`plugin.json`)
3. 构建生产版本
4. 测试UPX包
5. 发布到uTools插件市场

## 🐛 调试技巧

### 开发者工具
- 使用Chrome DevTools调试前端
- 使用uTools开发者工具调试插件
- 查看控制台日志和错误信息

### 常见问题
1. **API调用失败**
   - 检查网络连接
   - 验证API密钥
   - 查看API文档和限制

2. **截图功能异常**
   - 检查权限设置
   - 验证Electron API调用
   - 测试不同操作系统

3. **数据存储问题**
   - 检查uTools数据库API
   - 验证数据格式
   - 处理存储空间限制

## 🔄 版本管理

### Git工作流
```bash
# 创建功能分支
git checkout -b feature/new-feature

# 提交更改
git add .
git commit -m "feat: add new feature"

# 合并到主分支
git checkout main
git merge feature/new-feature
```

### 版本号规范
遵循语义化版本控制 (SemVer)：
- 主版本号：不兼容的API修改
- 次版本号：向下兼容的功能性新增
- 修订号：向下兼容的问题修正

## 📚 参考资料

### 官方文档
- [uTools开发者文档](https://www.u-tools.cn/docs/developer/)
- [Vue 3文档](https://vuejs.org/)
- [Element Plus文档](https://element-plus.org/)
- [Vite文档](https://vitejs.dev/)

### API文档
- [百度OCR API](https://ai.baidu.com/tech/ocr)
- [腾讯云OCR API](https://cloud.tencent.com/product/ocr)
- [阿里云OCR API](https://help.aliyun.com/product/442.html)
- [OpenAI API](https://platform.openai.com/docs)

## 🤝 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

### 提交信息规范
```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```
