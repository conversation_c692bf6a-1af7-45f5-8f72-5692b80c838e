// uTools API类型定义
declare global {
  interface Window {
    utools?: {
      // 基础API
      getPayload(): any
      copyText(text: string): void
      hideMainWindow(): void
      showMainWindow(): void
      setExpendHeight(height: number): void

      // 数据库API
      db?: {
        get(id: string): any
        put(doc: any): any
        remove(doc: any): any
        allDocs(): any
      }

      // 其他API
      getPath(name: string): string
      onPluginEnter(callback: (payload: any) => void): void
      onPluginReady(callback: () => void): void
      onPluginDetach(callback: () => void): void
    }

    // preload API
    preload?: {
      captureScreen(): Promise<string>
      readFileAsBase64(filePath: string): Promise<string>
      saveFile(filePath: string, data: any): boolean
      getSystemInfo(): {
        platform: string
        arch: string
        version: string
        homedir: string
        tmpdir: string
      }
      openFolder(folderPath: string): boolean
      copyToClipboard(text: string): boolean
      getImageFromClipboard(): string | null
      compressImage(imageData: string, quality?: number): string
      getFileInfo(filePath: string): {
        size: number
        isFile: boolean
        isDirectory: boolean
        mtime: Date
        ctime: Date
      }
      createTempFile(data: any, extension?: string): string
      deleteFile(filePath: string): boolean
      fileExists(filePath: string): boolean
      getAppDataPath(): string
    }
  }
}

// Vue组件Props类型
export interface ScreenshotOCRProps {}

export interface ImageOCRProps {
  imageData: string
}

export interface FileOCRProps {
  filePaths: string[]
}

export interface SettingsProps {
  modelValue: boolean
}

export interface HistoryProps {
  modelValue: boolean
}

// OCR相关类型
export interface OCRConfig {
  service: 'baidu' | 'tencent' | 'aliyun' | 'ai'
  config: any
}

export interface OCRResult {
  text: string
  confidence?: number
  words?: Array<{
    text: string
    confidence: number
    location?: {
      left: number
      top: number
      width: number
      height: number
    }
  }>
}

// 历史记录类型
export interface HistoryRecord {
  id: string
  image: string
  text: string
  timestamp: Date
  type: 'screenshot' | 'image' | 'file'
  fileName?: string
  confidence?: number
}

// 设置类型
export interface Settings {
  ocr: {
    defaultService: string
    baidu: {
      apiKey: string
      secretKey: string
      type: string
    }
    tencent: {
      secretId: string
      secretKey: string
      region: string
    }
    aliyun: {
      accessKeyId: string
      accessKeySecret: string
    }
    ai: {
      provider: string
      apiKey: string
      baseUrl: string
      model: string
      stream: boolean
    }
  }
  general: {
    autoCopy: boolean
    saveHistory: boolean
    maxHistoryCount: number
    screenshotHotkey: string
  }
}

export {}
