<template>
  <div class="file-ocr">
    <el-card class="main-card">
      <template #header>
        <div class="card-header">
          <span>文件OCR识别</span>
          <el-button-group>
            <el-button :icon="Setting" @click="showSettings = true">设置</el-button>
            <el-button :icon="Clock" @click="showHistory = true">历史</el-button>
          </el-button-group>
        </div>
      </template>

      <div class="content">
        <!-- 文件列表 -->
        <div class="file-list">
          <h3>待识别文件 ({{ filePaths.length }})</h3>
          <el-scrollbar height="200px">
            <div class="file-item" v-for="(file, index) in filePaths" :key="index">
              <el-icon><Document /></el-icon>
              <span class="file-name">{{ getFileName(file) }}</span>
              <el-button size="small" @click="previewFile(file)">预览</el-button>
            </div>
          </el-scrollbar>
        </div>

        <!-- 批量操作 -->
        <div class="batch-actions">
          <el-button 
            type="primary" 
            :icon="DocumentCopy"
            :loading="isRecognizing"
            @click="recognizeAllFiles"
          >
            {{ isRecognizing ? `识别中... (${currentIndex}/${filePaths.length})` : '批量识别' }}
          </el-button>
          <el-progress 
            v-if="isRecognizing" 
            :percentage="progress" 
            :show-text="false"
            style="margin-top: 10px;"
          />
        </div>

        <!-- 识别结果 -->
        <div class="results-section" v-if="results.length > 0">
          <h3>识别结果</h3>
          <el-tabs v-model="activeTab" type="card">
            <el-tab-pane 
              v-for="(result, index) in results" 
              :key="index"
              :label="getFileName(result.filePath)"
              :name="index.toString()"
            >
              <div class="result-content">
                <div class="result-header">
                  <span>{{ getFileName(result.filePath) }}</span>
                  <el-button-group>
                    <el-button size="small" :icon="CopyDocument" @click="copyResult(result.text)">复制</el-button>
                    <el-button size="small" :icon="Edit" @click="editResult(index)">编辑</el-button>
                  </el-button-group>
                </div>
                <el-input
                  v-model="result.text"
                  type="textarea"
                  :rows="8"
                  :readonly="!result.isEditing"
                />
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
    </el-card>

    <!-- 文件预览对话框 -->
    <el-dialog v-model="showPreview" title="文件预览" width="60%">
      <div class="preview-content">
        <img :src="previewImage" alt="文件预览" style="max-width: 100%; max-height: 400px;" />
      </div>
    </el-dialog>

    <!-- 设置对话框 -->
    <Settings v-model="showSettings" />
    
    <!-- 历史记录对话框 -->
    <History v-model="showHistory" />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Document, DocumentCopy, Setting, Clock, CopyDocument, Edit } from '@element-plus/icons-vue'
import Settings from './Settings.vue'
import History from './History.vue'
import { ocrService } from '@/services/ocrService'
import { historyService } from '@/services/historyService'

interface Props {
  filePaths: string[]
}

interface OCRResult {
  filePath: string
  text: string
  isEditing: boolean
}

const props = defineProps<Props>()

const results = ref<OCRResult[]>([])
const isRecognizing = ref(false)
const currentIndex = ref(0)
const progress = ref(0)
const activeTab = ref('0')
const showPreview = ref(false)
const previewImage = ref('')
const showSettings = ref(false)
const showHistory = ref(false)

onMounted(() => {
  // 自动开始识别第一个文件
  if (props.filePaths.length === 1) {
    recognizeAllFiles()
  }
})

// 获取文件名
const getFileName = (filePath: string) => {
  return filePath.split(/[/\\]/).pop() || filePath
}

// 预览文件
const previewFile = async (filePath: string) => {
  try {
    const imageData = await window.preload?.readFileAsBase64(filePath)
    if (imageData) {
      previewImage.value = `data:image/*;base64,${imageData}`
      showPreview.value = true
    }
  } catch (error) {
    ElMessage.error('预览失败: ' + error)
  }
}

// 批量识别文件
const recognizeAllFiles = async () => {
  if (props.filePaths.length === 0) return
  
  try {
    isRecognizing.value = true
    results.value = []
    currentIndex.value = 0
    
    for (let i = 0; i < props.filePaths.length; i++) {
      currentIndex.value = i + 1
      progress.value = Math.round((i / props.filePaths.length) * 100)
      
      const filePath = props.filePaths[i]
      const imageData = await window.preload?.readFileAsBase64(filePath)
      
      if (imageData) {
        const text = await ocrService.recognizeImage(`data:image/*;base64,${imageData}`)
        
        results.value.push({
          filePath,
          text,
          isEditing: false
        })
        
        // 保存到历史记录
        historyService.addRecord({
          id: Date.now().toString() + i,
          image: `data:image/*;base64,${imageData}`,
          text,
          timestamp: new Date(),
          type: 'file',
          fileName: getFileName(filePath)
        })
      }
    }
    
    progress.value = 100
    ElMessage.success(`成功识别 ${results.value.length} 个文件`)
  } catch (error) {
    ElMessage.error('识别失败: ' + error)
  } finally {
    isRecognizing.value = false
  }
}

// 复制结果
const copyResult = (text: string) => {
  if (window.utools) {
    window.utools.copyText(text)
    ElMessage.success('已复制到剪贴板')
  }
}

// 编辑结果
const editResult = (index: number) => {
  results.value[index].isEditing = !results.value[index].isEditing
}
</script>

<style scoped>
.file-ocr {
  padding: 20px;
  height: 100vh;
  box-sizing: border-box;
}

.main-card {
  height: 100%;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content {
  height: calc(100% - 60px);
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.file-list h3 {
  margin: 0 0 10px 0;
}

.file-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 10px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 5px;
}

.file-name {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.batch-actions {
  text-align: center;
}

.results-section {
  flex: 1;
  overflow: hidden;
}

.results-section h3 {
  margin: 0 0 10px 0;
}

.result-content {
  height: 300px;
  display: flex;
  flex-direction: column;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.preview-content {
  text-align: center;
}
</style>
