# 🔧 快速修复指南

## 当前状态
✅ 项目结构已创建完成
✅ 核心代码已实现
✅ TypeScript配置已修复
⚠️ 需要安装依赖才能运行

## 🚀 立即开始

### 方法1: 使用简化启动脚本
```bash
node start-dev.js
```
这个脚本会自动：
- 检查并安装依赖
- 启动开发服务器
- 显示使用说明

### 方法2: 手动步骤
```bash
# 1. 安装依赖
npm install
# 或使用pnpm（推荐）
pnpm install

# 2. 启动开发服务器
npm run dev
```

## 🎯 在uTools中测试

1. **打开uTools开发者工具**
   - 快捷键: `Ctrl/Cmd + Shift + D`

2. **新建插件项目**
   - 点击"新建"
   - 选择项目根目录的 `plugin.json` 文件

3. **启动插件**
   - 点击"启动"按钮
   - 在uTools中输入"截图OCR"测试

## 🔧 已修复的问题

### TypeScript配置
- ✅ 修复了`__dirname`在ES模块中的问题
- ✅ 简化了vite.config.ts配置
- ✅ 添加了可选类型标记避免运行时错误

### 依赖管理
- ✅ 创建了自动安装脚本
- ✅ 简化了构建配置
- ✅ 移除了复杂的插件依赖

### 代码结构
- ✅ 所有组件都已创建
- ✅ 服务层已实现
- ✅ 类型定义已完善

## 📝 下一步操作

### 1. 准备logo图标
创建一个64x64像素的PNG图标，命名为`logo.png`放在根目录

### 2. 配置API密钥
参考`config-examples.json`文件，在插件设置中配置：
- 百度OCR API密钥
- 或其他OCR服务密钥
- 或AI大模型API密钥

### 3. 测试功能
- 截图OCR: 输入"截图OCR"
- 图片OCR: 拖拽图片到uTools
- 设置: 输入"OCR设置"

## 🐛 常见问题解决

### Q: 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

### Q: 开发服务器启动失败
```bash
# 检查端口是否被占用
netstat -ano | findstr :5173
# 或修改vite.config.ts中的端口
```

### Q: uTools中插件无法加载
- 确保选择了正确的plugin.json文件
- 检查开发服务器是否正在运行
- 查看uTools开发者工具的错误信息

### Q: API调用失败
- 检查网络连接
- 验证API密钥是否正确
- 查看浏览器控制台的错误信息

## 📚 完整文档

- `README.md` - 完整使用说明
- `DEVELOPMENT.md` - 开发者指南
- `PROJECT_SUMMARY.md` - 项目总结
- `config-examples.json` - 配置示例

## 🎉 项目特色

这个OCR插件包含：
- 🖼️ 多种识别方式（截图/图片/文件）
- 🤖 AI大模型支持（GPT-4V/Claude/Gemini）
- 📊 智能历史管理
- ⚙️ 灵活配置系统
- 🎨 现代化UI界面

---

**提示**: 如果遇到任何问题，请查看控制台错误信息，大多数问题都有明确的错误提示。
