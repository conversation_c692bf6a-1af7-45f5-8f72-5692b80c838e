const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')

console.log('🚀 uTools OCR插件开发环境设置')
console.log('================================')

// 检查Node.js版本
function checkNodeVersion() {
  const nodeVersion = process.version
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0])
  
  console.log(`📋 检查Node.js版本: ${nodeVersion}`)
  
  if (majorVersion < 16) {
    console.error('❌ 需要Node.js 16或更高版本')
    process.exit(1)
  }
  
  console.log('✅ Node.js版本符合要求')
}

// 检查包管理器
function checkPackageManager() {
  console.log('📦 检查包管理器...')
  
  try {
    execSync('pnpm --version', { stdio: 'ignore' })
    console.log('✅ 检测到pnpm，推荐使用')
    return 'pnpm'
  } catch {
    try {
      execSync('npm --version', { stdio: 'ignore' })
      console.log('✅ 检测到npm')
      return 'npm'
    } catch {
      console.error('❌ 未找到npm或pnpm')
      process.exit(1)
    }
  }
}

// 安装依赖
function installDependencies(packageManager) {
  console.log('📥 安装项目依赖...')
  
  try {
    execSync(`${packageManager} install`, { stdio: 'inherit' })
    console.log('✅ 依赖安装完成')
  } catch (error) {
    console.error('❌ 依赖安装失败:', error.message)
    process.exit(1)
  }
}

// 检查必要文件
function checkRequiredFiles() {
  console.log('📄 检查必要文件...')
  
  const requiredFiles = [
    'package.json',
    'plugin.json',
    'preload.js',
    'index.html',
    'vite.config.ts',
    'tsconfig.json'
  ]
  
  const missingFiles = requiredFiles.filter(file => 
    !fs.existsSync(path.resolve(__dirname, '..', file))
  )
  
  if (missingFiles.length > 0) {
    console.error('❌ 缺少必要文件:', missingFiles.join(', '))
    process.exit(1)
  }
  
  console.log('✅ 所有必要文件都存在')
}

// 创建logo文件提醒
function checkLogo() {
  const logoPath = path.resolve(__dirname, '..', 'logo.png')
  
  if (!fs.existsSync(logoPath)) {
    console.log('⚠️  注意: 缺少logo.png文件')
    console.log('   请参考logo-placeholder.md创建64x64的PNG图标')
    console.log('   或使用在线工具生成图标')
  } else {
    console.log('✅ logo.png文件存在')
  }
}

// 创建开发配置
function createDevConfig() {
  console.log('⚙️  创建开发配置...')
  
  const configPath = path.resolve(__dirname, '..', '.env.development')
  
  if (!fs.existsSync(configPath)) {
    const devConfig = `# 开发环境配置
VITE_DEV_MODE=true
VITE_API_BASE_URL=http://localhost:5173
`
    
    fs.writeFileSync(configPath, devConfig)
    console.log('✅ 创建开发配置文件: .env.development')
  }
}

// 显示下一步操作
function showNextSteps(packageManager) {
  console.log('\n🎉 环境设置完成！')
  console.log('================================')
  console.log('📝 下一步操作:')
  console.log('')
  console.log('1. 启动开发服务器:')
  console.log(`   ${packageManager} run dev`)
  console.log('')
  console.log('2. 在uTools中调试:')
  console.log('   - 打开uTools开发者工具')
  console.log('   - 新建插件项目')
  console.log('   - 选择项目根目录的plugin.json文件')
  console.log('')
  console.log('3. 构建生产版本:')
  console.log(`   ${packageManager} run build`)
  console.log('')
  console.log('4. 构建UPX包:')
  console.log(`   ${packageManager} run build:upx`)
  console.log('')
  console.log('📚 更多信息请查看:')
  console.log('   - README.md: 使用说明')
  console.log('   - DEVELOPMENT.md: 开发指南')
  console.log('')
  console.log('🐛 遇到问题?')
  console.log('   - 检查Node.js版本 (需要16+)')
  console.log('   - 确保uTools已安装')
  console.log('   - 查看控制台错误信息')
}

// 主函数
function main() {
  try {
    checkNodeVersion()
    checkRequiredFiles()
    const packageManager = checkPackageManager()
    installDependencies(packageManager)
    checkLogo()
    createDevConfig()
    showNextSteps(packageManager)
  } catch (error) {
    console.error('❌ 设置过程中出现错误:', error.message)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main()
}

module.exports = { main }
