{"description": "OCR插件配置示例 - 请根据实际情况修改", "examples": {"baidu_ocr": {"name": "百度OCR配置示例", "config": {"defaultService": "baidu", "baidu": {"apiKey": "your_baidu_api_key_here", "secretKey": "your_baidu_secret_key_here", "type": "general_basic"}}, "获取方式": ["1. 访问 https://ai.baidu.com/", "2. 登录并创建应用", "3. 在应用管理中获取API Key和Secret Key", "4. 选择合适的识别类型：", "   - general_basic: 通用文字识别", "   - accurate_basic: 通用文字识别(高精度版)", "   - general: 通用文字识别(含位置信息版)"]}, "tencent_ocr": {"name": "腾讯云OCR配置示例", "config": {"defaultService": "tencent", "tencent": {"secretId": "your_tencent_secret_id_here", "secretKey": "your_tencent_secret_key_here", "region": "ap-beijing"}}, "获取方式": ["1. 访问 https://console.cloud.tencent.com/", "2. 开通文字识别OCR服务", "3. 在访问管理中创建API密钥", "4. 获取SecretId和SecretKey", "5. 选择合适的地域"]}, "aliyun_ocr": {"name": "阿里云OCR配置示例", "config": {"defaultService": "<PERSON><PERSON><PERSON>", "aliyun": {"accessKeyId": "your_aliyun_access_key_id_here", "accessKeySecret": "your_aliyun_access_key_secret_here"}}, "获取方式": ["1. 访问 https://ecs.console.aliyun.com/", "2. 开通印刷文字识别服务", "3. 在AccessKey管理中创建密钥", "4. 获取AccessKey ID和AccessKey Secret"]}, "openai_gpt4v": {"name": "OpenAI GPT-4V配置示例", "config": {"defaultService": "ai", "ai": {"provider": "openai", "apiKey": "sk-your_openai_api_key_here", "baseUrl": "https://api.openai.com", "model": "gpt-4-vision-preview", "stream": false}}, "获取方式": ["1. 访问 https://platform.openai.com/", "2. 注册并登录账户", "3. 在API Keys页面创建新的API密钥", "4. 确保账户有足够的余额", "5. 可选：配置自定义API地址（如使用代理）"], "注意事项": ["- GPT-4V需要付费使用", "- 建议开启流式输出获得更好体验", "- 可以使用gpt-4o等更新的模型"]}, "claude3": {"name": "Claude 3配置示例", "config": {"defaultService": "ai", "ai": {"provider": "claude", "apiKey": "sk-ant-your_claude_api_key_here", "model": "claude-3-opus-20240229", "stream": false}}, "获取方式": ["1. 访问 https://console.anthropic.com/", "2. 注册并登录账户", "3. 在API Keys页面创建新的API密钥", "4. 选择合适的模型版本"], "可用模型": ["claude-3-opus-20240229", "claude-3-sonnet-20240229", "claude-3-haiku-20240307"]}, "gemini": {"name": "Google Gemini配置示例", "config": {"defaultService": "ai", "ai": {"provider": "gemini", "apiKey": "your_gemini_api_key_here", "model": "gemini-pro-vision", "stream": false}}, "获取方式": ["1. 访问 https://ai.google.dev/", "2. 登录Google账户", "3. 在Google AI Studio中获取API密钥", "4. 选择合适的模型"]}}, "general_settings": {"name": "通用设置示例", "config": {"autoCopy": true, "saveHistory": true, "maxHistoryCount": 100, "screenshotHotkey": "Ctrl+Alt+S"}, "说明": {"autoCopy": "识别完成后自动复制结果到剪贴板", "saveHistory": "是否保存识别历史记录", "maxHistoryCount": "最大历史记录数量", "screenshotHotkey": "截图快捷键（暂未实现）"}}, "使用说明": ["1. 这是配置示例文件，不会被插件直接使用", "2. 请在插件的设置界面中配置相应的API密钥", "3. 不同的OCR服务有不同的计费方式和限制", "4. 建议先使用免费额度测试功能", "5. API密钥请妥善保管，不要泄露给他人"], "性能对比": {"百度OCR": {"优点": ["识别准确率高", "支持多种文字类型", "有免费额度"], "缺点": ["需要实名认证", "API调用有限制"], "适用场景": "通用文字识别，中文效果好"}, "腾讯云OCR": {"优点": ["识别速度快", "支持多种格式", "价格相对便宜"], "缺点": ["免费额度较少", "需要实名认证"], "适用场景": "批量处理，商业应用"}, "阿里云OCR": {"优点": ["技术成熟", "服务稳定", "支持多语言"], "缺点": ["价格较高", "配置相对复杂"], "适用场景": "企业级应用，高精度要求"}, "AI大模型OCR": {"优点": ["理解能力强", "可处理复杂场景", "支持多语言"], "缺点": ["成本较高", "速度相对较慢", "需要网络连接"], "适用场景": "复杂文档，多语言混合，表格识别"}}}