# uTools OCR插件

一个功能强大的OCR文字识别插件，支持多种OCR服务接口和AI大模型。

## 🌟 主要功能

### 📸 截图OCR
- 快捷键触发屏幕截图
- 实时文字识别
- 一键复制结果

### 🖼️ 图片OCR
- 支持拖拽图片识别
- 支持多种图片格式
- 批量处理功能

### 📁 文件OCR
- 批量文件识别
- 支持常见图片格式
- 进度显示和错误处理

### ⚙️ 多服务支持
- **百度OCR**: 通用文字识别、高精度版本
- **腾讯云OCR**: 印刷体文字识别
- **阿里云OCR**: 通用文字识别
- **AI大模型OCR**: 
  - OpenAI GPT-4V
  - Claude 3 Vision
  - Google Gemini Vision
  - 通义千问VL

### 📊 智能管理
- 历史记录保存
- 搜索和筛选
- 数据导入导出
- 统计分析

## 🚀 快速开始

### 安装插件
1. 下载最新版本的 `.upx` 文件
2. 在uTools中打开插件管理
3. 点击"安装插件"，选择下载的文件
4. 安装完成后即可使用

### 基础配置
1. 输入 `OCR设置` 打开配置页面
2. 选择要使用的OCR服务
3. 填入相应的API密钥
4. 保存设置

### 使用方法
- **截图OCR**: 输入 `截图OCR` 或 `OCR识别`
- **图片识别**: 直接拖拽图片到uTools
- **文件识别**: 选择图片文件后拖拽到uTools
- **查看历史**: 输入 `OCR历史` 或 `识别记录`

## 🔧 API配置指南

### 百度OCR
1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 创建应用获取 API Key 和 Secret Key
3. 在插件设置中填入密钥信息

### 腾讯云OCR
1. 访问 [腾讯云控制台](https://console.cloud.tencent.com/)
2. 开通文字识别服务
3. 获取 SecretId 和 SecretKey

### 阿里云OCR
1. 访问 [阿里云控制台](https://ecs.console.aliyun.com/)
2. 开通印刷文字识别服务
3. 获取 AccessKey ID 和 AccessKey Secret

### AI大模型配置
#### OpenAI GPT-4V
- API Key: 从OpenAI官网获取
- Base URL: 可选，支持自定义API地址
- 模型: gpt-4-vision-preview 或 gpt-4o

#### Claude 3
- API Key: 从Anthropic官网获取
- 模型: claude-3-opus-20240229 等

#### Google Gemini
- API Key: 从Google AI Studio获取
- 模型: gemini-pro-vision

## 📝 使用技巧

### 提高识别准确率
1. 确保图片清晰度足够
2. 避免倾斜和变形
3. 选择合适的OCR服务
4. 对于复杂文档，建议使用AI大模型

### 批量处理
1. 选择多个图片文件
2. 拖拽到uTools触发批量识别
3. 支持进度显示和错误重试

### 历史管理
1. 自动保存识别结果
2. 支持搜索和筛选
3. 可导出为JSON格式
4. 定期清理过期记录

## 🛠️ 开发说明

### 技术栈
- Vue 3 + TypeScript
- Element Plus UI
- Vite 构建工具
- uTools 插件框架

### 项目结构
```
src/
├── components/          # Vue组件
├── services/           # 业务服务
├── types/             # 类型定义
└── main.ts            # 入口文件
```

### 本地开发
```bash
# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 构建生产版本
npm run build

# 构建UPX包
npm run build:upx
```

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📞 支持

如有问题，请通过以下方式联系：
- GitHub Issues
- 邮箱: <EMAIL>

---

**注意**: 使用本插件需要相应的API服务密钥，请确保遵守各服务商的使用条款。
