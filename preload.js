const { ipc<PERSON><PERSON><PERSON>, desktopCapturer, nativeImage, clipboard, shell } = require('electron')
const fs = require('fs')
const path = require('path')
const os = require('os')

// 截图功能
async function captureScreen() {
  try {
    // 获取屏幕源
    const sources = await desktopCapturer.getSources({
      types: ['screen'],
      thumbnailSize: { width: 1920, height: 1080 }
    })

    if (sources.length === 0) {
      throw new Error('无法获取屏幕源')
    }

    // 使用第一个屏幕源
    const source = sources[0]
    const image = source.thumbnail

    // 转换为base64
    const base64 = image.toDataURL()
    return base64
  } catch (error) {
    console.error('截图失败:', error)
    throw error
  }
}

// 读取文件为base64
function readFileAsBase64(filePath) {
  try {
    const buffer = fs.readFileSync(filePath)
    const base64 = buffer.toString('base64')
    return base64
  } catch (error) {
    console.error('读取文件失败:', error)
    throw error
  }
}

// 保存文件
function saveFile(filePath, data) {
  try {
    fs.writeFileSync(filePath, data)
    return true
  } catch (error) {
    console.error('保存文件失败:', error)
    throw error
  }
}

// 获取系统信息
function getSystemInfo() {
  return {
    platform: os.platform(),
    arch: os.arch(),
    version: os.version(),
    homedir: os.homedir(),
    tmpdir: os.tmpdir()
  }
}

// 打开文件夹
function openFolder(folderPath) {
  try {
    shell.openPath(folderPath)
    return true
  } catch (error) {
    console.error('打开文件夹失败:', error)
    throw error
  }
}

// 复制到剪贴板
function copyToClipboard(text) {
  try {
    clipboard.writeText(text)
    return true
  } catch (error) {
    console.error('复制失败:', error)
    throw error
  }
}

// 从剪贴板获取图片
function getImageFromClipboard() {
  try {
    const image = clipboard.readImage()
    if (image.isEmpty()) {
      return null
    }
    return image.toDataURL()
  } catch (error) {
    console.error('获取剪贴板图片失败:', error)
    return null
  }
}

// 压缩图片
function compressImage(imageData, quality = 0.8) {
  try {
    const image = nativeImage.createFromDataURL(imageData)
    const compressed = image.toJPEG(Math.floor(quality * 100))
    return `data:image/jpeg;base64,${compressed.toString('base64')}`
  } catch (error) {
    console.error('压缩图片失败:', error)
    throw error
  }
}

// 获取文件信息
function getFileInfo(filePath) {
  try {
    const stats = fs.statSync(filePath)
    return {
      size: stats.size,
      isFile: stats.isFile(),
      isDirectory: stats.isDirectory(),
      mtime: stats.mtime,
      ctime: stats.ctime
    }
  } catch (error) {
    console.error('获取文件信息失败:', error)
    throw error
  }
}

// 创建临时文件
function createTempFile(data, extension = '.tmp') {
  try {
    const tempDir = os.tmpdir()
    const fileName = `ocr_temp_${Date.now()}${extension}`
    const filePath = path.join(tempDir, fileName)
    
    fs.writeFileSync(filePath, data)
    return filePath
  } catch (error) {
    console.error('创建临时文件失败:', error)
    throw error
  }
}

// 删除文件
function deleteFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath)
      return true
    }
    return false
  } catch (error) {
    console.error('删除文件失败:', error)
    throw error
  }
}

// 检查文件是否存在
function fileExists(filePath) {
  try {
    return fs.existsSync(filePath)
  } catch (error) {
    return false
  }
}

// 获取应用数据目录
function getAppDataPath() {
  const userDataPath = utools.getPath('userData')
  const appDataPath = path.join(userDataPath, 'ocr-plugin')
  
  // 确保目录存在
  if (!fs.existsSync(appDataPath)) {
    fs.mkdirSync(appDataPath, { recursive: true })
  }
  
  return appDataPath
}

// 导出API
window.preload = {
  captureScreen,
  readFileAsBase64,
  saveFile,
  getSystemInfo,
  openFolder,
  copyToClipboard,
  getImageFromClipboard,
  compressImage,
  getFileInfo,
  createTempFile,
  deleteFile,
  fileExists,
  getAppDataPath
}

// uTools API增强
window.utools = {
  ...utools,
  // 增强的复制功能
  copyText: (text) => {
    utools.copyText(text)
    copyToClipboard(text) // 双重保险
  },
  
  // 增强的获取payload功能
  getPayload: () => {
    const payload = utools.getPayload()
    if (!payload) return null
    
    // 处理不同类型的payload
    if (payload.type === 'files') {
      return {
        ...payload,
        data: payload.data || []
      }
    } else if (payload.type === 'img') {
      return {
        ...payload,
        data: payload.data || ''
      }
    }
    
    return payload
  }
}

// 监听uTools事件
utools.onPluginEnter(({ code, type, payload }) => {
  console.log('插件进入:', { code, type, payload })
  
  // 根据不同的功能代码执行相应操作
  switch (code) {
    case 'screenshot-ocr':
      // 截图OCR逻辑
      break
    case 'image-ocr':
      // 图片OCR逻辑
      break
    case 'file-ocr':
      // 文件OCR逻辑
      break
    case 'settings':
      // 设置页面逻辑
      break
    case 'history':
      // 历史记录逻辑
      break
  }
})

utools.onPluginReady(() => {
  console.log('OCR插件已准备就绪')
})

utools.onPluginDetach(() => {
  console.log('OCR插件已分离')
  // 清理临时文件等
})
