// 简化的开发启动脚本
const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 启动uTools OCR插件开发环境')
console.log('================================')

// 检查package.json是否存在
if (!fs.existsSync('package.json')) {
  console.error('❌ 未找到package.json文件')
  process.exit(1)
}

// 检查node_modules是否存在
if (!fs.existsSync('node_modules')) {
  console.log('📦 检测到依赖未安装，正在安装...')
  
  // 尝试使用pnpm，如果失败则使用npm
  const installProcess = spawn('pnpm', ['install'], { stdio: 'inherit', shell: true })
  
  installProcess.on('error', () => {
    console.log('📦 pnpm不可用，使用npm安装依赖...')
    const npmProcess = spawn('npm', ['install'], { stdio: 'inherit', shell: true })
    
    npmProcess.on('close', (code) => {
      if (code === 0) {
        startDev()
      } else {
        console.error('❌ 依赖安装失败')
        process.exit(1)
      }
    })
  })
  
  installProcess.on('close', (code) => {
    if (code === 0) {
      startDev()
    } else {
      console.error('❌ 依赖安装失败')
      process.exit(1)
    }
  })
} else {
  startDev()
}

function startDev() {
  console.log('🔧 启动开发服务器...')
  
  // 启动vite开发服务器
  const devProcess = spawn('npm', ['run', 'dev'], { stdio: 'inherit', shell: true })
  
  devProcess.on('close', (code) => {
    console.log(`开发服务器已停止，退出码: ${code}`)
  })
  
  // 显示使用说明
  setTimeout(() => {
    console.log('\n📝 使用说明:')
    console.log('1. 开发服务器已启动在 http://localhost:5173')
    console.log('2. 在uTools开发者工具中加载 plugin.json 文件')
    console.log('3. 修改代码会自动热重载')
    console.log('4. 按 Ctrl+C 停止开发服务器')
  }, 3000)
}
