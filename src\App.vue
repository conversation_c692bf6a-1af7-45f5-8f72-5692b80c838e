<template>
  <div class="app-container">
    <!-- 截图OCR -->
    <ScreenshotOCR v-if="currentFeature === 'screenshot-ocr'" />
    
    <!-- 图片OCR -->
    <ImageOCR v-else-if="currentFeature === 'image-ocr'" :image-data="imageData" />
    
    <!-- 文件OCR -->
    <FileOCR v-else-if="currentFeature === 'file-ocr'" :file-paths="filePaths" />
    
    <!-- 设置页面 -->
    <Settings v-else-if="currentFeature === 'settings'" />
    
    <!-- 历史记录 -->
    <History v-else-if="currentFeature === 'history'" />
    
    <!-- 默认页面 -->
    <div v-else class="welcome-page">
      <el-empty description="请选择功能">
        <el-button type="primary" @click="handleScreenshot">开始截图OCR</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import ScreenshotOCR from './components/ScreenshotOCR.vue'
import ImageOCR from './components/ImageOCR.vue'
import FileOCR from './components/FileOCR.vue'
import Settings from './components/Settings.vue'
import History from './components/History.vue'

const currentFeature = ref<string>('')
const imageData = ref<string>('')
const filePaths = ref<string[]>([])

onMounted(() => {
  // 获取uTools传入的功能代码和数据
  if (window.utools) {
    const payload = window.utools.getPayload()
    currentFeature.value = payload?.code || 'screenshot-ocr'
    
    // 处理不同类型的输入数据
    if (payload?.type === 'img') {
      imageData.value = payload.data
    } else if (payload?.type === 'files') {
      filePaths.value = payload.data
    }
  }
})

const handleScreenshot = () => {
  currentFeature.value = 'screenshot-ocr'
}
</script>

<style scoped>
.app-container {
  height: 100vh;
  background: #f5f5f5;
}

.welcome-page {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}
</style>
