import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
// import { utools } from '@qc2168/vite-plugin-utools'

export default defineConfig({
  plugins: [
    vue(),
    // utools({
    //   external: ['electron', 'fs', 'path', 'crypto', 'os'],
    //   preload: {
    //     watch: true,
    //     name: 'preload.js'
    //   },
    //   buildUpx: true
    // })
  ],
  resolve: {
    alias: {
      '@': '/src'
    }
  },
  build: {
    outDir: 'dist'
  },
  server: {
    port: 5173,
    host: true
  }
})
