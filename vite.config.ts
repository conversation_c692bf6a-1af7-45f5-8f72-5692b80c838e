import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'
import { utools } from '@qc2168/vite-plugin-utools'

export default defineConfig({
  plugins: [
    vue(),
    utools({
      external: ['electron', 'fs', 'path', 'crypto', 'os'],
      preload: {
        watch: true,
        name: 'preload.js'
      },
      buildUpx: true
    })
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  build: {
    outDir: 'dist',
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html')
      }
    }
  },
  server: {
    port: 5173,
    host: true
  }
})
