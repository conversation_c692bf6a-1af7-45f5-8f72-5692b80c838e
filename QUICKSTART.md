# 🚀 快速开始指南

## 📋 环境要求

- Node.js 16+
- uTools 客户端
- 代码编辑器（推荐VS Code）

## ⚡ 一键设置

```bash
# 克隆项目（如果还没有）
git clone <repository-url>
cd utools-ocr-plugin

# 运行自动设置脚本
npm run setup
```

设置脚本会自动：

- ✅ 安装项目依赖
- ✅ 检查必要文件
- ✅ 创建开发配置
- ✅ 显示下一步操作

## 🛠️ 手动设置（可选）

如果自动设置失败，可以手动执行：

```bash
# 1. 安装依赖
npm install
# 或使用pnpm（推荐）
pnpm install

# 2. 启动开发服务器
npm run dev
```

## 🎯 在uTools中调试

### 1. 打开uTools开发者工具

- 快捷键：`Ctrl/Cmd + Shift + D`
- 或在uTools设置中找到"开发者工具"

### 2. 新建插件项目

- 点击"新建"按钮
- 选择项目根目录下的 `plugin.json` 文件
- 填写插件信息

### 3. 启动插件

- 点击"启动"按钮
- 在uTools中输入插件关键词测试

## 🔧 开发流程

### 1. 修改代码

- 编辑 `src/` 目录下的文件
- 热重载会自动更新页面

### 2. 测试功能

- 在uTools中测试各项功能
- 查看控制台日志和错误

### 3. 构建发布

```bash
# 构建生产版本
npm run build

# 构建UPX安装包
npm run build:upx
```

## 📝 配置API密钥

### 百度OCR

1. 访问 [百度AI开放平台](https://ai.baidu.com/)
2. 创建应用获取API Key和Secret Key
3. 在插件中输入"OCR设置"进行配置

### OpenAI GPT-4V

1. 访问 [OpenAI平台](https://platform.openai.com/)
2. 获取API Key
3. 在插件设置中配置

### 其他服务

参考README.md中的详细配置说明

## 🧪 测试功能

### 截图OCR

1. 在uTools中输入"截图OCR"
2. 点击"开始截图"按钮
3. 选择屏幕区域进行识别

### 图片OCR

1. 拖拽图片到uTools
2. 选择"图片OCR识别"
3. 查看识别结果

### 文件OCR

1. 选择图片文件拖拽到uTools
2. 选择"文件OCR识别"
3. 批量处理多个文件

## 🐛 常见问题

### Q: 开发服务器启动失败

A: 检查端口5173是否被占用，或修改vite.config.ts中的端口配置

### Q: 插件在uTools中无法加载

A: 确保plugin.json文件路径正确，检查开发模式配置

### Q: API调用失败

A: 检查网络连接和API密钥配置，查看控制台错误信息

### Q: 截图功能不工作

A: 检查系统权限设置，确保允许屏幕录制

## 📚 更多资源

- [完整文档](README.md)
- [开发指南](DEVELOPMENT.md)
- [uTools官方文档](https://www.u-tools.cn/docs/developer/)
- [Vue 3文档](https://vuejs.org/)

## 🤝 获得帮助

- 查看项目Issues
- 阅读开发文档
- 加入开发者社区

---

**提示**: 首次开发uTools插件？建议先阅读[uTools开发者文档](https://www.u-tools.cn/docs/developer/)了解基础概念。
