const fs = require('fs')
const path = require('path')
const archiver = require('archiver')

// 构建UPX包
async function buildUpx() {
  const distPath = path.resolve(__dirname, '../dist')
  const upxPath = path.resolve(__dirname, '../utools-ocr-plugin.upx')
  
  // 检查dist目录是否存在
  if (!fs.existsSync(distPath)) {
    console.error('dist目录不存在，请先运行 npm run build')
    process.exit(1)
  }
  
  // 创建压缩包
  const output = fs.createWriteStream(upxPath)
  const archive = archiver('zip', {
    zlib: { level: 9 } // 最高压缩级别
  })
  
  output.on('close', () => {
    console.log(`UPX包构建完成: ${upxPath}`)
    console.log(`文件大小: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`)
  })
  
  archive.on('error', (err) => {
    console.error('构建UPX包失败:', err)
    process.exit(1)
  })
  
  archive.pipe(output)
  
  // 添加dist目录下的所有文件
  archive.directory(distPath, false)
  
  // 完成压缩
  await archive.finalize()
}

// 复制必要文件到dist目录
function copyRequiredFiles() {
  const files = [
    { src: 'plugin.json', dest: 'dist/plugin.json' },
    { src: 'preload.js', dest: 'dist/preload.js' },
    { src: 'logo.png', dest: 'dist/logo.png', optional: true },
    { src: 'README.md', dest: 'dist/README.md', optional: true }
  ]
  
  files.forEach(file => {
    const srcPath = path.resolve(__dirname, '..', file.src)
    const destPath = path.resolve(__dirname, '..', file.dest)
    
    if (fs.existsSync(srcPath)) {
      fs.copyFileSync(srcPath, destPath)
      console.log(`已复制: ${file.src} -> ${file.dest}`)
    } else if (!file.optional) {
      console.error(`必需文件不存在: ${file.src}`)
      process.exit(1)
    }
  })
}

// 主函数
async function main() {
  console.log('开始构建UPX包...')
  
  // 复制必要文件
  copyRequiredFiles()
  
  // 构建UPX包
  await buildUpx()
  
  console.log('构建完成！')
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error)
}

module.exports = { buildUpx, copyRequiredFiles }
